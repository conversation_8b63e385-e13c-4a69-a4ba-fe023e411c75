<?php

namespace App\Http\Controllers;

use App\Repositories\Expense\ExpenseInterface;
use App\Repositories\Leave\LeaveInterface;
use App\Repositories\LeaveMaster\LeaveMasterInterface;
use App\Repositories\SchoolSetting\SchoolSettingInterface;
use App\Repositories\SessionYear\SessionYearInterface;
use App\Repositories\Staff\StaffInterface;
use App\Services\BootstrapTableService;
use App\Services\CachingService;
use App\Services\ResponseService;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\Payroll;
use App\Models\LeaveDetail;
use App\Models\PayrollSetting;
use App\Models\Staff;
use PDF;
use Throwable;

class PayrollController extends Controller {
    private SessionYearInterface $sessionYear;
    private StaffInterface $staff;
    private ExpenseInterface $expense;
    private LeaveMasterInterface $leaveMaster;
    private CachingService $cache;
    private SchoolSettingInterface $schoolSetting;
    private LeaveInterface $leave;
    private SessionYearInterface $sessionYearInterface;

    public function __construct(SessionYearInterface $sessionYear, StaffInterface $staff, ExpenseInterface $expense, LeaveMasterInterface $leaveMaster, CachingService $cache, SchoolSettingInterface $schoolSetting, LeaveInterface $leave, SessionYearInterface $sessionYearInterface) {
        $this->sessionYear = $sessionYear;
        $this->staff = $staff;
        $this->expense = $expense;
        $this->leaveMaster = $leaveMaster;
        $this->cache = $cache;
        $this->schoolSetting = $schoolSetting;
        $this->leave = $leave;
        $this->sessionYearInterface = $sessionYearInterface;
    }

    public function index() {
        //
        ResponseService::noFeatureThenRedirect('Expense Management');
        ResponseService::noPermissionThenRedirect('payroll-list');

        $sessionYear = $this->sessionYear->builder()->orderBy('start_date', 'ASC')->first();
        $sessionYear = date('Y', strtotime($sessionYear->start_date));
        // Get months starting from session year
        $months = sessionYearWiseMonth();
        

        return view('payroll.index', compact('sessionYear', 'months'));
    }

    public function create() {
        //
        ResponseService::noFeatureThenRedirect('Expense Management');
        ResponseService::noPermissionThenRedirect('payroll-create');
    }

    public function store(Request $request) {
        //
        ResponseService::noFeatureThenRedirect('Expense Management');
        ResponseService::noPermissionThenRedirect('payroll-create');

        $request->validate([
            'net_salary' => 'required',
            'date'       => 'required',
            'user_id' => 'required'
        ], [
            'net_salary.required' => trans('no_records_found'),
            'user_id.required' => trans('Please select at least one record')
        ]);

        try {
            DB::beginTransaction();
            $user_ids = explode(",",$request->user_id);
            
            $selectedMonth = $request->month;
            $selectedYear = $request->year;
            // Define the start and end dates
            $startDate = Carbon::createFromFormat('Y-m', "$selectedYear-$selectedMonth")->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();

            $school = Auth::user()->school; 

            if (!$school) {
                ResponseService::errorResponse('School not found');
            }
            $sessionYearInterface = $this->sessionYearInterface->builder()->where(function ($query) use ($startDate, $endDate) {
                $query->where(function ($query) use ($startDate, $endDate) {
                    $query->where('start_date', '<=', $endDate)
                        ->where('end_date', '>=', $startDate);
                });
            })->first();

            if (!$sessionYearInterface) {
                ResponseService::errorResponse('Session year not found');
            }

            $data = array();
            foreach ($user_ids as $key => $user_id) {
                // $basic_salary = $request->basic_salary[$user_id];
                // $allowance = $request->allowance[$user_id] ?? 0;
                // $claims = $request->claims[$user_id] ?? 0;
                // $earn_others = $request->earn_others[$user_id] ?? 0;
                // $deduction_others = $request->deduction_others[$user_id] ?? 0;
                // $employee_epf = $request->employee_epf[$user_id] ?? 0;
                // $employee_socso = $request->employee_socso[$user_id] ?? 0;
                // $employee_eis = $request->employee_eis[$user_id] ?? 0;
                // $employee_pcb = $request->employee_pcb[$user_id] ?? 0;



                // $earning = $allowance + $claims + $earn_others;
                // $deduction = (($basic_salary / 30) * $leave_without_pay) + $deduction_others + $employee_epf + $employee_socso + $employee_eis + $employee_pcb;
                // $net_salary = $basic_salary + ($earning - $deduction);
                
                $data[] = [
                    'title'            => Carbon::create()->month($request->month)->format('F') . ' - ' . $request->year,
                    'description'      => 'Salary',
                    'month'            => $request->month,
                    'year'             => $request->year,    
                    'staff_id'         => $user_id,
                    'basic_salary'     => $request->basic_salary[$user_id],
                    'paid_leaves'      => $request->paid_leave[$user_id],
                    'amount'           => $request->net_salary[$user_id],
                    'session_year_id'  => $sessionYearInterface->id,
                    'date'             => date('Y-m-d', strtotime($request->date)),
                    'allowance'        => $request->allowance[$user_id],
                    'claims'           => $request->claims[$user_id],
                    'earn_others'      => $request->earn_others[$user_id],
                    'earn_remark'      => $request->earn_remark[$user_id],
                    'deduction_others' => $request->deduction_others[$user_id],
                    'deduction_remark' => $request->deduction_remark[$user_id],
                    'leave_without_pay'=> $request->leave_without_pay[$user_id] ?? 0,
                    'employee_epf'     => $request->employee_epf[$user_id],
                    'employee_socso'   => $request->employee_socso[$user_id],
                    'employee_eis'     => $request->employee_eis[$user_id],  
                    'employee_pcb'     => $request->employee_pcb[$user_id],
                    'employer_epf'     => $request->employer_epf[$user_id],
                    'employer_socso'   => $request->employer_socso[$user_id],
                    'employer_eis'     => $request->employer_eis[$user_id], 
                ];
            }
            
            $this->expense->upsert($data, ['staff_id', 'month', 'year'], ['amount', 'session_year_id','basic_salary','date','allowance','claims','earn_others','earn_remark','deduction_others','deduction_remark','leave_without_pay','employee_epf','employee_socso','employee_eis','employee_pcb','employer_epf','employer_socso','employer_eis']);

            DB::commit();
            ResponseService::successResponse('Data Stored Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, 'Payroll Controller -> Store method');
            ResponseService::errorResponse();
        }
    }

    public function show() {
        ResponseService::noFeatureThenRedirect('Expense Management');
        ResponseService::noPermissionThenRedirect('payroll-list');

        $sort = request('sort', 'rank');
        $order = request('order', 'ASC');
        $search = request('search');
        $month = request('month');
        $year = request('year');

        

        $leaveMaster = $this->leaveMaster->builder()->whereHas('session_year', function ($q) use ($month, $year) {
            $q->where(function ($q) use ($month, $year) {
                $q->whereMonth('start_date', '<=', $month)->whereYear('start_date', $year);
            })->orWhere(function ($q) use ($month, $year) {
                $q->whereMonth('start_date', '>=', $month)->whereYear('end_date', '<=', $year);
            });
        })->first();

        $sql = $this->staff->builder()->with(['user', 'expense', 'leave' => function ($q) use ($month,$year) {
            $q->where('status', 1)->withCount(['leave_detail as full_leave' => function ($q) use ($month,$year) {
                $q->whereMonth('date', $month)->whereYear('date',$year)->where('type', 'Full');
            }])->withCount(['leave_detail as half_leave' => function ($q) use ($month,$year) {
                $q->whereMonth('date', $month)->whereYear('date',$year)->whereNot('type', 'Full');
            }]);
        }])->whereHas('user', function ($q) {
            $q->whereNull('deleted_at')->Owner();
        })->when($search, function ($query) use ($search) {
            $query->where(function ($query) use ($search) {
                $query->orwhereHas('user', function ($q) use ($search) {
                    $q->where('first_name', 'LIKE', "%$search%")->orwhere('last_name', 'LIKE', "%$search%");
                });
            });
        });

        $total = $sql->count();

        $sql->orderBy($sort, $order);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;
        foreach ($res as $row) {
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $salary_deduction = 0;
            $salary = $row->salary;
            $full_leave = isset($row->leave) ? $row->leave->sum('full_leave') : 0;
            $half_leave = isset($row->leave) ? ($row->leave->sum('half_leave') / 2) : 0;
            $total_leave = $full_leave + $half_leave;
            $paid_leave = $leaveMaster->leaves ?? 0;
            // $tempRow['leave_without_pay'] = max($total_leave - $paid_leave,0);
            $tempRow['total_leaves'] = $total_leave;
            $tempRow['salary_deduction'] = number_format($salary_deduction, 2);
            $tempRow['employee_epf'] = 0;
            $tempRow['employer_epf'] = 0;
            $tempRow['employee_socso'] = 0;
            $tempRow['employer_socso'] = 0;
            $tempRow['employee_eis'] = 0;
            $tempRow['employer_eis'] = 0;
            if($salary){
                $epf = DB::select("SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)",[$salary,$salary]);
                if($epf){
                    // Check if the staff is 60 years or older
                    $age = Carbon::parse($row->user->dob)->age;
                    if ($age >= 60) {
                        $tempRow['employee_socso'] = 0;

                        // Hardcode employer SOCSO for age 60+ with salary 1101-1200 to RM14.40
                        if ($salary >= 1101 && $salary <= 1200) {
                            $tempRow['employer_socso'] = 14.40;
                        } else {
                            $tempRow['employer_socso'] = 0;
                        }
                            
                        $tempRow['employee_eis'] = 0;
                        $tempRow['employer_eis'] = 0;
                    } else {
                        $tempRow['employee_socso'] = $epf[0]->socso_employee ?? 0;
                        $tempRow['employer_socso'] = $epf[0]->socso_employer ?? 0;
                        $tempRow['employee_eis'] = $epf[0]->eis_employee ?? 0;
                        $tempRow['employer_eis'] = $epf[0]->eis_employer ?? 0;
                    }
                }
                $tempRow['employee_epf'] = round($salary * 0.11,2);
                $tempRow['employer_epf'] = round($salary * 0.13,2);
            }
            if (isset($row->expense)) {
                // TODO : this line can be converted into filter searching instead of searching from query
                $expense = $row->expense()->where('month', $month)->where('year', $year)->first();
                if ($expense) {
                    $operate = BootstrapTableService::button('fa fa-file-o', url('payroll/slip/'.$expense->id), ['btn-gradient-info'], ['title' => trans("slip"),'target' => '_blank']);
                    $operate .= BootstrapTableService::deleteButton(route('payroll.destroy', $expense->id));
                    $status = 1;
                    $tempRow['salary'] = $expense->basic_salary;
                    $salary = $expense->getRawOriginal('basic_salary');

                    $tempRow['status'] = $status;
                    $tempRow['paid_leaves'] = $expense->paid_leaves;
                    // if ($expense->paid_leaves < $total_leave) {
                    //     $unpaid_leave = $total_leave - $expense->paid_leaves;
                    //     $per_day_salary = $salary / 30;
                    //     $salary_deduction = $unpaid_leave * $per_day_salary;
                    //     $tempRow['salary_deduction'] = number_format($salary_deduction,2);
                    //     $tempRow['net_salary'] = $expense->amount;
                    // }
                    $tempRow['net_salary'] = $expense->amount;
                    $tempRow['allowance'] = $expense->allowance;
                    $tempRow['claims'] = $expense->claims;
                    $tempRow['earn_others'] = $expense->earn_others;
                    $tempRow['earn_remark'] = $expense->earn_remark;
                    $tempRow['deduction_others'] = $expense->deduction_others;
                    $tempRow['deduction_remark'] = $expense->deduction_remark;
                    $tempRow['employee_epf'] = $expense->employee_epf;
                    $tempRow['employee_socso'] = $expense->employee_socso;
                    $tempRow['employee_eis'] = $expense->employee_eis;
                    $tempRow['employee_pcb'] = $expense->employee_pcb;
                    $tempRow['employer_epf'] = $expense->employer_epf;
                    $tempRow['employer_socso'] = $expense->employer_socso;
                    $tempRow['employer_eis'] = $expense->employer_eis;
                    // $tempRow['operate'] = $operate;
                } else if ($leaveMaster) {
                    $salary = $row->salary;
                    $tempRow['paid_leaves'] = $leaveMaster->leaves;
                    // if ($leaveMaster->leaves < $total_leave) {
                    //     if ($leaveMaster->leaves) {
                    //         $unpaid_leave = $total_leave - $leaveMaster->leaves;
                    //         $per_day_salary = $salary / 30;
                    //         $salary_deduction = $unpaid_leave * $per_day_salary;
                    //     }
                    //     $tempRow['salary_deduction'] = number_format($salary_deduction,2);
                    // }
                    $tempRow['net_salary'] = $salary - ($tempRow['employee_epf'] + $tempRow['employee_socso'] + $tempRow['employee_eis']);
                    $operate = BootstrapTableService::button('fa fa-eye',  'javascript:void(0);', ['btn-gradient-success','preview'], ['title' => trans("preview"),'data-id' => $row->user_id]);
                } else {
                    $tempRow['net_salary'] = $salary - ($tempRow['employee_epf'] + $tempRow['employee_socso'] + $tempRow['employee_eis']);
                    $operate = BootstrapTableService::button('fa fa-eye',  'javascript:void(0);', ['btn-gradient-success','preview'], ['title' => trans("preview"),'data-id' => $row->user_id]);
                }
                $tempRow['operate'] = $operate;
            }
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function slip_index()
    {
        ResponseService::noFeatureThenRedirect('Expense Management');
        try {
            $sessionYear = $this->sessionYear->builder()->pluck('name','id');
            $currentSessionYear = $this->cache->getDefaultSessionYear();

            $FirstsessionYear = $this->sessionYear->builder()->orderBy('start_date', 'ASC')->first();
            $FirstsessionYear = date('Y', strtotime($FirstsessionYear->start_date));
            
            return view('payroll.list',compact('sessionYear','currentSessionYear','FirstsessionYear'));
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th, 'Payroll Controller -> Slip Index method');
            ResponseService::errorResponse();
        }
    }

    public function slip_list()
    {
        ResponseService::noFeatureThenRedirect('Expense Management');

        $offset = request('offset', 0);
        $limit = request('limit', 10);
        $sort = request('sort', 'rank');
        $order = request('order', 'ASC');
        $search = request('search');
        $year = request('year');

        $sql = $this->expense->builder()->where('staff_id',Auth::user()->staff->id)
        ->where(function($q) use($search){
            $q->when($search, function($q) use($search) {
                $q->where('title','LIKE',"%$search%")
                ->orWhere('basic_salary','LIKE',"%$search%")
                ->orWhere('amount','LIKE',"%$search%")
                ->where('staff_id',Auth::user()->staff->id);
            });
        })
        
        ->when($year, function($q) use($year) {
            $q->whereYear('date',$year);
        })
        ->where('staff_id',Auth::user()->staff->id);

        $total = $sql->count();

        $sql->orderBy($sort, $order)->skip($offset)->take($limit);
        $res = $sql->get();

        $bulkData = array();
        $bulkData['total'] = $total;
        $rows = array();
        $no = 1;

        foreach ($res as $row) {
            $operate = BootstrapTableService::button('fa fa-file-o', url('payroll/slip/'.$row->id), ['btn-gradient-info'], ['title' => trans("slip"),'target' => '_blank']);
            $tempRow = $row->toArray();
            $tempRow['no'] = $no++;
            $tempRow['operate'] = $operate;
            $rows[] = $tempRow;
        }

        $bulkData['rows'] = $rows;
        return response()->json($bulkData);
    }

    public function slip($id = null)
    {
        ResponseService::noFeatureThenRedirect('Expense Management');
        try {
            $vertical_logo = $this->schoolSetting->builder()->where('name', 'vertical_logo')->first();
            $schoolSetting = $this->cache->getSchoolSettings();

            // Salary
            $salary = $this->expense->builder()->with('staff.user:id,first_name,last_name')->where('id',$id)->first();
            if (!$salary) {
                return redirect()->back()->with('error',trans('no_data_found'));
            }
            // Get total leaves
            $leaves = $this->leave->builder()->where('status',1)->where('user_id',$salary->staff->user_id)->withCount(['leave_detail as full_leave' => function ($q) use ($salary) {
                $q->whereMonth('date', $salary->month)->whereYear('date',$salary->year)->where('type', 'Full');
            }])->withCount(['leave_detail as half_leave' => function ($q) use ($salary) {
                $q->whereMonth('date', $salary->month)->whereYear('date',$salary->year)->whereNot('type', 'Full');
            }])->get();

            $total_leaves = $leaves->sum('full_leave') + ($leaves->sum('half_leave') / 2);
            // Total days
            $days = Carbon::now()->year($salary->year)->month($salary->month)->daysInMonth;

            $pdf = PDF::loadView('payroll.slip',compact('vertical_logo','schoolSetting','salary','total_leaves','days'));
            return $pdf->stream($salary->title.'-'.$salary->staff->user->full_name.'.pdf');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }

    public function updateEpfContribution(Request $request)
    {
        $basic_salary = $request->basic_salary;

        // Fetch EPF contributions based on the salary range
        $epf = DB::select(
            "SELECT * FROM epf_contributions WHERE min_range <= ? AND (max_range >= ? OR max_range IS NULL)",
            [$basic_salary, $basic_salary]
        );

        $tempRow = [];
        
        if ($epf) {
            // Check if the staff is 60 years or older
            $staff_id = $request->staff_id;
            $staff = $this->staff->builder()->with('user')->find($staff_id);
            $age = Carbon::parse($staff->user->dob)->age;

            if ($age >= 60) {
                $tempRow['employee_socso'] = 0;

                // Hardcode employer SOCSO for age 60+ with salary 1101-1200 to RM14.40
                if ($salary >= 1101 && $salary <= 1200) {
                    $tempRow['employer_socso'] = 14.40;
                } else {
                    $tempRow['employer_socso'] = 0;
                }

                $tempRow['employee_eis'] = 0;
                $tempRow['employer_eis'] = 0;
            } else {
                $tempRow['employee_socso'] = $epf[0]->socso_employee;
                $tempRow['employer_socso'] = $epf[0]->socso_employer;
                $tempRow['employee_eis'] = $epf[0]->eis_employee;
                $tempRow['employer_eis'] = $epf[0]->eis_employer;
            }
        }
        
        // Calculate EPF
        $tempRow['employee_epf'] = round($basic_salary * 0.11, 2);
        $tempRow['employer_epf'] = round($basic_salary * 0.13, 2);

        // Return the data as a JSON response
        return response()->json($tempRow);
    }

    public function destroy($id)
    {
        ResponseService::noFeatureThenRedirect('Expense Management');
        ResponseService::noPermissionThenRedirect('payroll-list');

        try {
            DB::beginTransaction();
            DB::table('expenses')->where('id',$id)->delete();
            DB::commit();
            ResponseService::successResponse('Data Deleted Successfully');
        } catch (Throwable $e) {
            DB::rollBack();
            ResponseService::logErrorResponse($e, "Attendance Controller -> Destroy Method");
            ResponseService::errorResponse();
        }
    }

    public function preview(Request $request)
    {
        ResponseService::noFeatureThenRedirect('Expense Management');
        try {
            $vertical_logo = $this->schoolSetting->builder()->where('name', 'vertical_logo')->first();
            $schoolSetting = $this->cache->getSchoolSettings();

            $previewData = $request->previewData;
            $userId = $previewData['user_id'];
            $month = $previewData['month'];
            $year = $previewData['year'];
            $month_name = date("F", mktime(0, 0, 0, $month, 10)); 
            $title = $month_name . " - " .$year;
            $userDetails = DB::select("SELECT *,CONCAT(users.first_name, ' ', users.last_name) AS full_name FROM users,staffs WHERE users.id = staffs.user_id AND staffs.user_id = ?",[$userId]);
            $pdf = PDF::loadView('payroll.preview',compact('vertical_logo','schoolSetting','title','previewData','userDetails'));
            return $pdf->stream($title.'-'.$userDetails[0]->full_name.'.pdf');
        } catch (\Throwable $th) {
            ResponseService::logErrorResponse($th);
            ResponseService::errorResponse();
        }
    }
    
    public function customIndex(Request $request)
    {
    }
    
    public function customShow(Request $request, $id)
    {
    }
    
    public function customUpdate(Request $request, $id)
    {
    }
    
    public function customDestroy(Request $request, $id)
    {

    }
    
}
